require('dotenv').config();
const express = require('express');
const session = require('express-session');
const passport = require('passport');
const OpenIDConnectStrategy = require('passport-openidconnect').Strategy;

const app = express();
const port = process.env.PORT || 3000;

// Environment variables validation
const requiredEnvVars = ['OKTA_CLIENT_ID', 'OKTA_CLIENT_SECRET', 'OKTA_ISSUER'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  console.error('Please set the following environment variables:');
  console.error('- OKTA_CLIENT_ID: Your Okta application client ID');
  console.error('- OKTA_CLIENT_SECRET: Your Okta application client secret');
  console.error('- OKTA_ISSUER: Your Okta domain (e.g., https://your-domain.okta.com)');
  process.exit(1);
}

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to true in production with HTTPS
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  },
  name: 'okta.session'
}));

// Passport configuration
app.use(passport.initialize());
app.use(passport.session());

// Passport serialization
passport.serializeUser((user, done) => {
  done(null, user);
});

passport.deserializeUser((user, done) => {
  done(null, user);
});

// OpenID Connect Strategy
passport.use('oidc', new OpenIDConnectStrategy({
  issuer: process.env.OKTA_ISSUER,
  authorizationURL: `${process.env.OKTA_ISSUER}/v1/authorize`,
  tokenURL: `${process.env.OKTA_ISSUER}/v1/token`,
  userInfoURL: `${process.env.OKTA_ISSUER}/v1/userinfo`,
  clientID: process.env.OKTA_CLIENT_ID,
  clientSecret: process.env.OKTA_CLIENT_SECRET,
  callbackURL: process.env.CALLBACK_URL || 'http://localhost:3000/auth/callback',
  scope: 'openid profile email'
}, (issuer, sub, profile, accessToken, refreshToken, done) => {
  console.log('🔍 Authentication callback received:');
  console.log('  - Issuer:', issuer);
  console.log('  - Subject:', sub);
  console.log('  - Profile ID:', profile.id);
  console.log('  - Access Token:', accessToken ? 'Present' : 'Missing');
  console.log('  - Refresh Token:', refreshToken ? 'Present' : 'Missing');

  const user = {
    issuer,
    sub,
    profile,
    accessToken,
    refreshToken,
    tokenInfo: {
      access_token: accessToken,
      refresh_token: refreshToken,
      profile: profile._json,
      claims: profile._json
    }
  };

  console.log('✅ User object created successfully');
  return done(null, user);
}));

// Middleware to check authentication
function ensureAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.redirect('/');
}

// Routes
app.get('/', (req, res) => {
  console.log('🏠 Home route accessed');
  console.log('  - Session ID:', req.sessionID);
  console.log('  - Is Authenticated:', req.isAuthenticated());
  console.log('  - User present:', req.user ? 'Yes' : 'No');
  console.log('  - Error param:', req.query.error);

  if (req.isAuthenticated()) {
    console.log('✅ User is authenticated, redirecting to dashboard');
    res.redirect('/dashboard');
  } else {
    console.log('❌ User not authenticated, showing login page');
    const errorMessage = req.query.error ? `<div style="color: red; margin: 20px 0;">Authentication Error: ${req.query.error}</div>` : '';

    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Okta OIDC Authentication</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
          .container { text-align: center; }
          .btn { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
          .btn:hover { background-color: #0056b3; }
          .info { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: left; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🔐 Okta OIDC Authentication</h1>
          ${errorMessage}
          <p>Click the button below to authenticate with Okta and view your token information.</p>
          <a href="/auth/login" class="btn">Login with Okta</a>
          
          <div class="info">
            <h3>Environment Variables Required:</h3>
            <ul>
              <li><strong>OKTA_CLIENT_ID</strong>: Your Okta application client ID</li>
              <li><strong>OKTA_CLIENT_SECRET</strong>: Your Okta application client secret</li>
              <li><strong>OKTA_ISSUER</strong>: Your Okta domain (e.g., https://your-domain.okta.com)</li>
              <li><strong>CALLBACK_URL</strong> (optional): Defaults to http://localhost:3000/auth/callback</li>
            </ul>
          </div>
        </div>
      </body>
      </html>
    `);
  }
});

app.get('/auth/login', (req, res, next) => {
  console.log('🚀 Starting authentication with Okta...');
  passport.authenticate('oidc')(req, res, next);
});

app.get('/auth/callback', (req, res, next) => {
  console.log('📥 Callback received from Okta');
  console.log('  - Query params:', req.query);

  passport.authenticate('oidc', {
    failureRedirect: '/',
    failureFlash: false
  })(req, res, (err) => {
    if (err) {
      console.error('❌ Authentication error:', err);
      return res.redirect('/?error=auth_failed');
    }

    console.log('✅ Authentication successful, user:', req.user ? 'Present' : 'Missing');
    console.log('🔐 Session ID:', req.sessionID);
    console.log('👤 Is Authenticated:', req.isAuthenticated());

    if (req.isAuthenticated()) {
      console.log('🎉 Redirecting to dashboard');
      res.redirect('/dashboard');
    } else {
      console.log('❌ User not authenticated after callback');
      res.redirect('/?error=not_authenticated');
    }
  });
});

// Debug route
app.get('/debug', (req, res) => {
  res.json({
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated(),
    user: req.user,
    session: req.session,
    cookies: req.headers.cookie
  });
});

app.get('/dashboard', ensureAuthenticated, (req, res) => {
  const user = req.user;
  const tokenInfo = user.tokenInfo;
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Token Information - Okta OIDC</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .token-section { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .token-value { background-color: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; word-break: break-all; margin: 10px 0; }
        .btn { background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
        .btn:hover { background-color: #c82333; }
        .json-display { background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; overflow-x: auto; }
        .success { color: #28a745; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .info-grid { grid-template-columns: 1fr; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🎉 Authentication Successful!</h1>
        <p class="success">You have successfully authenticated with Okta OIDC</p>
        <a href="/logout" class="btn">Logout</a>
      </div>

      <div class="info-grid">
        <div class="token-section">
          <h2>🔑 Access Token</h2>
          <div class="token-value">${tokenInfo.access_token || 'Not available'}</div>
        </div>

        <div class="token-section">
          <h2>🔄 Refresh Token</h2>
          <div class="token-value">${tokenInfo.refresh_token || 'Not available'}</div>
        </div>
      </div>

      <div class="token-section">
        <h2>👤 User Profile Information</h2>
        <div class="json-display">${JSON.stringify(tokenInfo.profile, null, 2)}</div>
      </div>

      <div class="token-section">
        <h2>📋 Token Claims</h2>
        <div class="json-display">${JSON.stringify(tokenInfo.claims, null, 2)}</div>
      </div>

      <div class="token-section">
        <h2>🔍 Full User Object</h2>
        <div class="json-display">${JSON.stringify({
          issuer: user.issuer,
          subject: user.sub,
          hasAccessToken: !!user.accessToken,
          hasRefreshToken: !!user.refreshToken
        }, null, 2)}</div>
      </div>
    </body>
    </html>
  `);
});

app.get('/logout', (req, res) => {
  req.logout((err) => {
    if (err) {
      console.error('Logout error:', err);
    }
    req.session.destroy((err) => {
      if (err) {
        console.error('Session destroy error:', err);
      }
      res.redirect('/');
    });
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Application error:', err);
  res.status(500).send(`
    <h1>Error</h1>
    <p>An error occurred during authentication: ${err.message}</p>
    <a href="/">Go back to home</a>
  `);
});

app.listen(port, () => {
  console.log(`🚀 Server running at http://localhost:${port}`);
  console.log('📋 Environment variables configured:');
  console.log(`   - OKTA_CLIENT_ID: ${process.env.OKTA_CLIENT_ID ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - OKTA_CLIENT_SECRET: ${process.env.OKTA_CLIENT_SECRET ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - OKTA_ISSUER: ${process.env.OKTA_ISSUER || '❌ Missing'}`);
  console.log(`   - CALLBACK_URL: ${process.env.CALLBACK_URL || 'http://localhost:3000/auth/callback (default)'}`);
});
