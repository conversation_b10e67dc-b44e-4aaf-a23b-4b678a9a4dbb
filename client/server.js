require('dotenv').config();
const express = require('express');
const session = require('express-session');
const { ExpressOIDC } = require('@okta/oidc-middleware');
const { Kafka, logLevel } = require('kafkajs');

const app = express();
const port = process.env.PORT || 3000;

// Environment variables validation
const requiredEnvVars = ['OKTA_CLIENT_ID', 'OKTA_CLIENT_SECRET', 'OKTA_ISSUER'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  console.error('Please set the following environment variables:');
  console.error('- OKTA_CLIENT_ID: Your Okta application client ID');
  console.error('- OKTA_CLIENT_SECRET: Your Okta application client secret');
  console.error('- OKTA_ISSUER: Your Okta domain (e.g., https://your-domain.okta.com)');
  process.exit(1);
}

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-in-production',
  resave: true,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to true in production with HTTPS
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// OIDC configuration
const oidc = new ExpressOIDC({
  issuer: process.env.OKTA_ISSUER,
  client_id: process.env.OKTA_CLIENT_ID,
  client_secret: process.env.OKTA_CLIENT_SECRET,
  appBaseUrl: process.env.APP_BASE_URL || 'http://localhost:3000',
  scope: 'openid profile email',
  routes: {
    login: {
      path: '/auth/login'
    },
    loginCallback: {
      path: '/auth/callback',
      afterCallback: '/dashboard'
    }
  }
});

// Apply OIDC middleware
app.use(oidc.router);

// Kafka configuration function
async function listKafkaTopics(accessToken) {
  const bootstrapServers = process.env.KAFKA_BOOTSTRAP;

  if (!bootstrapServers) {
    throw new Error('KAFKA_BOOTSTRAP environment variable is not set');
  }

  const kafka = new Kafka({
    clientId: 'okta-kafka-web-client',
    brokers: bootstrapServers.split(','),
    logLevel: logLevel.ERROR, // Reduce logging for web interface
    ssl: false,
    sasl: {
      mechanism: 'oauthbearer',
      oauthBearerProvider: async () => {
        return {
          value: accessToken,
        };
      },
    },
  });

  const admin = kafka.admin();

  try {
    await admin.connect();
    const topics = await admin.listTopics();
    return topics;
  } finally {
    await admin.disconnect();
  }
}

// Middleware to check authentication
function ensureAuthenticated(req, res, next) {
  if (req.userContext) {
    return next();
  }
  res.redirect('/auth/login');
}

// Routes
app.get('/', (req, res) => {
  console.log('🏠 Home route accessed');
  console.log('  - Session ID:', req.sessionID);
  console.log('  - User Context:', req.userContext ? 'Present' : 'Missing');
  console.log('  - Error param:', req.query.error);

  if (req.userContext) {
    console.log('✅ User is authenticated, redirecting to dashboard');
    res.redirect('/dashboard');
  } else {
    console.log('❌ User not authenticated, showing login page');
    const errorMessage = req.query.error ? `<div style="color: red; margin: 20px 0;">Authentication Error: ${req.query.error}</div>` : '';

    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Okta OIDC Authentication</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
          .container { text-align: center; }
          .btn { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
          .btn:hover { background-color: #0056b3; }
          .info { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: left; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🔐 Okta OIDC Authentication</h1>
          ${errorMessage}
          <p>Click the button below to authenticate with Okta and view your token information.</p>
          <a href="/auth/login" class="btn">Login with Okta</a>
          
          <div class="info">
            <h3>Environment Variables Required:</h3>
            <ul>
              <li><strong>OKTA_CLIENT_ID</strong>: Your Okta application client ID</li>
              <li><strong>OKTA_CLIENT_SECRET</strong>: Your Okta application client secret</li>
              <li><strong>OKTA_ISSUER</strong>: Your Okta domain (e.g., https://your-domain.okta.com)</li>
              <li><strong>CALLBACK_URL</strong> (optional): Defaults to http://localhost:3000/auth/callback</li>
            </ul>
          </div>
        </div>
      </body>
      </html>
    `);
  }
});

// Authentication routes are handled by Okta OIDC middleware automatically

// Debug route
app.get('/debug', (req, res) => {
  res.json({
    sessionID: req.sessionID,
    userContext: req.userContext,
    session: req.session,
    cookies: req.headers.cookie
  });
});

app.get('/dashboard', ensureAuthenticated, (req, res) => {
  const userContext = req.userContext;
  const userInfo = userContext.userinfo;
  const tokens = userContext.tokens;
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Token Information - Okta OIDC</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .token-section { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .token-value { background-color: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; word-break: break-all; margin: 10px 0; }
        .btn { background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
        .btn:hover { background-color: #c82333; }
        .json-display { background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; overflow-x: auto; }
        .success { color: #28a745; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .info-grid { grid-template-columns: 1fr; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🎉 Authentication Successful!</h1>
        <p class="success">You have successfully authenticated with Okta OIDC</p>
        <a href="/kafka/topics" class="btn" style="background-color: #28a745; margin-right: 10px;">📋 List Kafka Topics</a>
        <a href="/logout" class="btn">Logout</a>
      </div>

      <div class="info-grid">
        <div class="token-section">
          <h2>🔑 Access Token</h2>
          <div class="token-value">${tokens.access_token || 'Not available'}</div>
        </div>

        <div class="token-section">
          <h2>🔄 Refresh Token</h2>
          <div class="token-value">${tokens.refresh_token || 'Not available'}</div>
        </div>
      </div>

      <div class="token-section">
        <h2>👤 User Profile Information</h2>
        <div class="json-display">${JSON.stringify(userInfo, null, 2)}</div>
      </div>

      <div class="token-section">
        <h2>📋 ID Token Claims</h2>
        <div class="json-display">${JSON.stringify(tokens.id_token ? JSON.parse(Buffer.from(tokens.id_token.split('.')[1], 'base64').toString()) : {}, null, 2)}</div>
      </div>

      <div class="token-section">
        <h2>🔍 Full Token Object</h2>
        <div class="json-display">${JSON.stringify({
          access_token: tokens.access_token ? 'Present' : 'Missing',
          refresh_token: tokens.refresh_token ? 'Present' : 'Missing',
          id_token: tokens.id_token ? 'Present' : 'Missing',
          token_type: tokens.token_type,
          expires_in: tokens.expires_in
        }, null, 2)}</div>
      </div>
    </body>
    </html>
  `);
});

// Kafka topics route
app.get('/kafka/topics', ensureAuthenticated, async (req, res) => {
  const userContext = req.userContext;
  const accessToken = userContext.tokens.access_token;

  console.log('📋 Kafka topics requested');
  console.log('  - Access Token:', accessToken ? 'Present' : 'Missing');

  try {
    const topics = await listKafkaTopics(accessToken);
    console.log(`✅ Retrieved ${topics.length} Kafka topics`);

    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Kafka Topics - Okta OIDC</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .btn { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
          .btn:hover { background-color: #0056b3; }
          .btn.success { background-color: #28a745; }
          .btn.success:hover { background-color: #218838; }
          .topics-container { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
          .topic-item { background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
          .topic-count { color: #28a745; font-weight: bold; }
          .no-topics { color: #dc3545; font-style: italic; }
          .error { color: #dc3545; background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>📋 Kafka Topics</h1>
          <p>Topics retrieved using your Okta access token</p>
          <a href="/dashboard" class="btn">🏠 Back to Dashboard</a>
          <a href="/kafka/topics" class="btn success">🔄 Refresh Topics</a>
          <a href="/logout" class="btn" style="background-color: #dc3545;">Logout</a>
        </div>

        <div class="topics-container">
          <h2>📊 Topic List</h2>
          <p class="topic-count">Found ${topics.length} topics</p>

          ${topics.length > 0 ?
            topics.map(topic => `<div class="topic-item">📄 ${topic}</div>`).join('') :
            '<p class="no-topics">No topics found in the Kafka cluster</p>'
          }
        </div>

        <div class="topics-container">
          <h3>🔧 Configuration Used</h3>
          <p><strong>Kafka Bootstrap Servers:</strong> ${process.env.KAFKA_BOOTSTRAP || 'Not configured'}</p>
          <p><strong>Authentication:</strong> OAuth Bearer Token from Okta</p>
          <p><strong>Client ID:</strong> okta-kafka-web-client</p>
        </div>
      </body>
      </html>
    `);

  } catch (error) {
    console.error('❌ Error listing Kafka topics:', error.message);

    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Kafka Topics Error - Okta OIDC</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .btn { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
          .btn:hover { background-color: #0056b3; }
          .error { color: #dc3545; background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .config-info { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>❌ Error Listing Kafka Topics</h1>
          <a href="/dashboard" class="btn">🏠 Back to Dashboard</a>
          <a href="/kafka/topics" class="btn" style="background-color: #28a745;">🔄 Try Again</a>
        </div>

        <div class="error">
          <h3>Error Details:</h3>
          <p>${error.message}</p>
        </div>

        <div class="config-info">
          <h3>🔧 Configuration Check</h3>
          <p><strong>Kafka Bootstrap Servers:</strong> ${process.env.KAFKA_BOOTSTRAP || '❌ Not configured - Please set KAFKA_BOOTSTRAP environment variable'}</p>
          <p><strong>Access Token:</strong> ${accessToken ? '✅ Present' : '❌ Missing'}</p>

          <h4>Common Issues:</h4>
          <ul>
            <li>KAFKA_BOOTSTRAP environment variable not set</li>
            <li>Kafka cluster not accessible from this network</li>
            <li>Access token doesn't have permission to access Kafka</li>
            <li>Kafka cluster requires different authentication method</li>
          </ul>
        </div>
      </body>
      </html>
    `);
  }
});

// Custom logout route
app.get('/logout', (req, res) => {
  console.log('🚪 Logout requested');
  if (req.userContext) {
    console.log('✅ User found, destroying session');
    // Clear the user context
    delete req.userContext;
    req.session.destroy((err) => {
      if (err) {
        console.error('❌ Session destroy error:', err);
      }
      console.log('🏠 Redirecting to home');
      res.redirect('/');
    });
  } else {
    console.log('❌ No user context found, redirecting to home');
    res.redirect('/');
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Application error:', err);
  res.status(500).send(`
    <h1>Error</h1>
    <p>An error occurred during authentication: ${err.message}</p>
    <a href="/">Go back to home</a>
  `);
});

oidc.on('ready', () => {
  app.listen(port, () => {
    console.log(`🚀 Server running at http://localhost:${port}`);
    console.log('📋 Environment variables configured:');
    console.log(`   - OKTA_CLIENT_ID: ${process.env.OKTA_CLIENT_ID ? '✅ Set' : '❌ Missing'}`);
    console.log(`   - OKTA_CLIENT_SECRET: ${process.env.OKTA_CLIENT_SECRET ? '✅ Set' : '❌ Missing'}`);
    console.log(`   - OKTA_ISSUER: ${process.env.OKTA_ISSUER || '❌ Missing'}`);
    console.log(`   - APP_BASE_URL: ${process.env.APP_BASE_URL || 'http://localhost:3000 (default)'}`);
    console.log('✅ Okta OIDC middleware ready');
  });
});

oidc.on('error', err => {
  console.error('❌ Okta OIDC middleware error:', err);
});
