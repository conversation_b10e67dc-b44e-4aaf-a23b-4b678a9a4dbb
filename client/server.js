require('dotenv').config();
const express = require('express');
const session = require('express-session');
const { ExpressOIDC } = require('@okta/oidc-middleware');

const app = express();
const port = process.env.PORT || 3000;

// Environment variables validation
const requiredEnvVars = ['OKTA_CLIENT_ID', 'OKTA_CLIENT_SECRET', 'OKTA_ISSUER'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  console.error('Please set the following environment variables:');
  console.error('- OKTA_CLIENT_ID: Your Okta application client ID');
  console.error('- OKTA_CLIENT_SECRET: Your Okta application client secret');
  console.error('- OKTA_ISSUER: Your Okta domain (e.g., https://your-domain.okta.com)');
  process.exit(1);
}

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-in-production',
  resave: true,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to true in production with HTTPS
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// OIDC configuration
const oidc = new ExpressOIDC({
  issuer: process.env.OKTA_ISSUER,
  client_id: process.env.OKTA_CLIENT_ID,
  client_secret: process.env.OKTA_CLIENT_SECRET,
  appBaseUrl: process.env.APP_BASE_URL || 'http://localhost:3000',
  scope: 'openid profile email',
  routes: {
    login: {
      path: '/auth/login'
    },
    loginCallback: {
      path: '/auth/callback',
      afterCallback: '/dashboard'
    },
    logout: {
      path: '/auth/logout',
      afterLogout: '/'
    }
  }
});

// Apply OIDC middleware
app.use(oidc.router);

// Middleware to check authentication
function ensureAuthenticated(req, res, next) {
  if (req.userContext) {
    return next();
  }
  res.redirect('/auth/login');
}

// Routes
app.get('/', (req, res) => {
  console.log('🏠 Home route accessed');
  console.log('  - Session ID:', req.sessionID);
  console.log('  - User Context:', req.userContext ? 'Present' : 'Missing');
  console.log('  - Error param:', req.query.error);

  if (req.userContext) {
    console.log('✅ User is authenticated, redirecting to dashboard');
    res.redirect('/dashboard');
  } else {
    console.log('❌ User not authenticated, showing login page');
    const errorMessage = req.query.error ? `<div style="color: red; margin: 20px 0;">Authentication Error: ${req.query.error}</div>` : '';

    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Okta OIDC Authentication</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
          .container { text-align: center; }
          .btn { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
          .btn:hover { background-color: #0056b3; }
          .info { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: left; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🔐 Okta OIDC Authentication</h1>
          ${errorMessage}
          <p>Click the button below to authenticate with Okta and view your token information.</p>
          <a href="/auth/login" class="btn">Login with Okta</a>
          
          <div class="info">
            <h3>Environment Variables Required:</h3>
            <ul>
              <li><strong>OKTA_CLIENT_ID</strong>: Your Okta application client ID</li>
              <li><strong>OKTA_CLIENT_SECRET</strong>: Your Okta application client secret</li>
              <li><strong>OKTA_ISSUER</strong>: Your Okta domain (e.g., https://your-domain.okta.com)</li>
              <li><strong>CALLBACK_URL</strong> (optional): Defaults to http://localhost:3000/auth/callback</li>
            </ul>
          </div>
        </div>
      </body>
      </html>
    `);
  }
});

// Authentication routes are handled by Okta OIDC middleware automatically

// Debug route
app.get('/debug', (req, res) => {
  res.json({
    sessionID: req.sessionID,
    userContext: req.userContext,
    session: req.session,
    cookies: req.headers.cookie
  });
});

app.get('/dashboard', ensureAuthenticated, (req, res) => {
  const userContext = req.userContext;
  const userInfo = userContext.userinfo;
  const tokens = userContext.tokens;
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Token Information - Okta OIDC</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .token-section { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .token-value { background-color: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; word-break: break-all; margin: 10px 0; }
        .btn { background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; }
        .btn:hover { background-color: #c82333; }
        .json-display { background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; overflow-x: auto; }
        .success { color: #28a745; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .info-grid { grid-template-columns: 1fr; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🎉 Authentication Successful!</h1>
        <p class="success">You have successfully authenticated with Okta OIDC</p>
        <a href="/auth/logout" class="btn">Logout</a>
      </div>

      <div class="info-grid">
        <div class="token-section">
          <h2>🔑 Access Token</h2>
          <div class="token-value">${tokens.access_token || 'Not available'}</div>
        </div>

        <div class="token-section">
          <h2>🔄 Refresh Token</h2>
          <div class="token-value">${tokens.refresh_token || 'Not available'}</div>
        </div>
      </div>

      <div class="token-section">
        <h2>👤 User Profile Information</h2>
        <div class="json-display">${JSON.stringify(userInfo, null, 2)}</div>
      </div>

      <div class="token-section">
        <h2>📋 ID Token Claims</h2>
        <div class="json-display">${JSON.stringify(tokens.id_token ? JSON.parse(Buffer.from(tokens.id_token.split('.')[1], 'base64').toString()) : {}, null, 2)}</div>
      </div>

      <div class="token-section">
        <h2>🔍 Full Token Object</h2>
        <div class="json-display">${JSON.stringify({
          access_token: tokens.access_token ? 'Present' : 'Missing',
          refresh_token: tokens.refresh_token ? 'Present' : 'Missing',
          id_token: tokens.id_token ? 'Present' : 'Missing',
          token_type: tokens.token_type,
          expires_in: tokens.expires_in
        }, null, 2)}</div>
      </div>
    </body>
    </html>
  `);
});

// Logout route is handled by Okta OIDC middleware automatically

// Error handling
app.use((err, req, res, next) => {
  console.error('Application error:', err);
  res.status(500).send(`
    <h1>Error</h1>
    <p>An error occurred during authentication: ${err.message}</p>
    <a href="/">Go back to home</a>
  `);
});

oidc.on('ready', () => {
  app.listen(port, () => {
    console.log(`🚀 Server running at http://localhost:${port}`);
    console.log('📋 Environment variables configured:');
    console.log(`   - OKTA_CLIENT_ID: ${process.env.OKTA_CLIENT_ID ? '✅ Set' : '❌ Missing'}`);
    console.log(`   - OKTA_CLIENT_SECRET: ${process.env.OKTA_CLIENT_SECRET ? '✅ Set' : '❌ Missing'}`);
    console.log(`   - OKTA_ISSUER: ${process.env.OKTA_ISSUER || '❌ Missing'}`);
    console.log(`   - APP_BASE_URL: ${process.env.APP_BASE_URL || 'http://localhost:3000 (default)'}`);
    console.log('✅ Okta OIDC middleware ready');
  });
});

oidc.on('error', err => {
  console.error('❌ Okta OIDC middleware error:', err);
});
