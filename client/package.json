{"name": "okta-oidc-client", "version": "1.0.0", "description": "Node.js application with Okta OIDC authentication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "list-topics": "node listTopics.js"}, "dependencies": {"kafkajs": "^2.2.4", "express": "^4.18.2", "express-session": "^1.17.3", "passport": "^0.7.0", "passport-openidconnect": "^0.1.1", "dotenv": "^16.3.1"}}